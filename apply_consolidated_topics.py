#!/usr/bin/env python3
"""
Apply LLM-consolidated topics to the server
"""

import requests
import json
import sys
from datetime import datetime

def apply_consolidated_topics(consolidated_file):
    """Apply consolidated topics from file to the server"""
    
    # Load consolidated data
    try:
        with open(consolidated_file, 'r', encoding='utf-8') as f:
            consolidated_data = json.load(f)
    except Exception as e:
        print(f"❌ Error loading consolidated file: {e}")
        return False
    
    # Validate data structure
    if 'topics' not in consolidated_data or 'people' not in consolidated_data:
        print(f"❌ Invalid consolidated data format")
        return False
    
    topics = consolidated_data['topics']
    people = consolidated_data['people']
    
    print(f"📊 Consolidated data loaded:")
    print(f"   Topics: {len(topics)}")
    print(f"   People: {len(people)}")
    
    # Apply to server
    try:
        print(f"\n🔄 Applying consolidated topics to server...")
        response = requests.post(
            'http://localhost:3001/api/apply-consolidated-topics',
            json=consolidated_data,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Successfully applied consolidated topics!")
            print(f"   {result.get('message', 'No message')}")
            return True
        else:
            print(f"❌ Server error: {response.status_code}")
            print(f"   {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error applying consolidated topics: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Applying LLM-consolidated topics to server...")
    print("="*60)
    
    # Find the most recent consolidated topics file
    import glob
    import os
    
    pattern = "data/consolidated_topics_*.json"
    files = glob.glob(pattern)
    
    if not files:
        print(f"❌ No consolidated topics files found matching pattern: {pattern}")
        return False
    
    # Get the most recent file
    latest_file = max(files, key=os.path.getctime)
    print(f"📁 Using consolidated file: {latest_file}")
    
    # Apply the consolidated topics
    success = apply_consolidated_topics(latest_file)
    
    if success:
        print(f"\n✅ Consolidated topics applied successfully!")
        print(f"\n💡 Next steps:")
        print(f"   1. Check the web interface to see the consolidated topics")
        print(f"   2. Verify that similar topics have been merged")
        print(f"   3. Run another consolidation if more duplicates are found")
        return True
    else:
        print(f"\n❌ Failed to apply consolidated topics!")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
