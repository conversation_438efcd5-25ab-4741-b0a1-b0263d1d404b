#!/usr/bin/env python3
"""
Aggressive topic consolidation script to merge very similar topics
This will handle the specific cases visible in the UI
"""

import requests
import json
import re
from collections import defaultdict

def get_all_topics():
    """Get all topics from the API"""
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                return result.get('topics', [])
        return []
    except Exception as e:
        print(f"Error fetching topics: {e}")
        return []

def normalize_topic_name(name):
    """Normalize topic name for aggressive matching"""
    # Convert to lowercase
    normalized = name.lower()
    
    # Remove common suffixes and variations
    patterns_to_remove = [
        r'项目$', r'工具$', r'系统$', r'平台$', r'网站$', r'应用$',
        r'tutor.*', r'后端.*', r'前端.*', r'界面.*', r'重构.*',
        r'改进.*', r'改造.*', r'设计.*', r'搭建.*', r'实现.*',
        r'调研.*', r'技术栈.*', r'开发.*', r'功能.*', r'demo.*'
    ]
    
    for pattern in patterns_to_remove:
        normalized = re.sub(pattern, '', normalized, flags=re.IGNORECASE)
    
    # Clean up whitespace and special characters
    normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', normalized)
    normalized = normalized.strip()
    
    return normalized

def find_consolidation_groups():
    """Find groups of topics that should be consolidated"""
    topics = get_all_topics()
    if not topics:
        return []
    
    # Separate people from topics
    actual_topics = [t for t in topics if t.get('category') != 'people']
    
    print(f"Analyzing {len(actual_topics)} topics for consolidation...")
    
    # Define specific consolidation rules
    consolidation_rules = {
        'ai口语': ['ai口语', 'ai口语工具', 'ai口语tutor', 'ai口语项目', 'ai外语口语工具', 'ai口语网站'],
        'ai工具': ['ai工具', 'ai口语工具', 'ai外语口语工具'],
        'ai日记': ['ai日记', 'ai日记项目', 'ai日记功能', 'ai日记demo'],
        'ai fintech': ['ai fintech', 'fintech', 'ai fintech项目', 'ai fintech技术', 'ai fintech技术栈'],
        '智能oa系统': ['智能oa', 'oa系统', '智能oa系统', '中船智能oa', 'oa系统信息'],
        '蓝领招聘平台': ['蓝领招聘', '蓝领招聘平台', '蓝领招聘项目', '蓝领招聘助理'],
        '生活成本计算': ['生活成本', '生活成本计算', '生活成本项目', 'easymentor生活成本'],
        '抖音项目': ['抖音', '抖音项目', '抖音账户', '抖音内容', '抖音背景调查'],
        '菜地': ['菜地', '菜地工作', '菜地规划', '菜地修整', '蔬菜园'],
        '投资项目挖掘': ['投资项目', '初创项目', '空头', '尽职调查', '投资标的'],
        'easymentor': ['easymentor', 'easymentor改版', 'easymentor计算器'],
        '猫': ['猫', '奶牛猫', '橘猫', '简州猫', '狸花猫', '猫粮', '喂猫']
    }
    
    # Group topics by consolidation rules
    consolidation_groups = []
    used_topic_ids = set()
    
    for target_name, patterns in consolidation_rules.items():
        group = []
        
        for topic in actual_topics:
            if topic['id'] in used_topic_ids:
                continue
                
            topic_normalized = normalize_topic_name(topic['name'])
            
            # Check if this topic matches any pattern
            for pattern in patterns:
                pattern_normalized = normalize_topic_name(pattern)
                if pattern_normalized in topic_normalized or topic_normalized in pattern_normalized:
                    group.append(topic)
                    used_topic_ids.add(topic['id'])
                    break
        
        if len(group) > 1:
            consolidation_groups.append({
                'target_name': target_name,
                'topics': group,
                'primary_topic': min(group, key=lambda t: len(t['name']))  # Shortest name as primary
            })
    
    return consolidation_groups

def show_consolidation_plan(groups):
    """Show what will be consolidated"""
    print(f"\n📋 Found {len(groups)} consolidation groups:")
    
    total_topics_to_merge = 0
    for i, group in enumerate(groups, 1):
        topics_to_merge = len(group['topics']) - 1
        total_topics_to_merge += topics_to_merge
        
        print(f"\n🔗 Group {i}: '{group['target_name']}' ({len(group['topics'])} topics)")
        print(f"  Primary: {group['primary_topic']['name']} ({group['primary_topic']['category']})")
        print(f"  Merging:")
        for topic in group['topics']:
            if topic['id'] != group['primary_topic']['id']:
                print(f"    - {topic['name']} ({topic['category']})")
    
    print(f"\n📊 Total: Will merge {total_topics_to_merge} topics into {len(groups)} consolidated topics")
    return total_topics_to_merge

def apply_consolidations(groups):
    """Apply the consolidations using server endpoints"""
    print(f"\n🚀 Applying consolidations...")
    
    success_count = 0
    for i, group in enumerate(groups, 1):
        try:
            print(f"\n🔄 Processing group {i}/{len(groups)}: {group['target_name']}")
            
            # For now, we'll use the cleanup endpoint which should merge similar topics
            # In a real implementation, we'd need specific merge endpoints
            
            # Update the primary topic name to the target name
            primary_topic = group['primary_topic']
            if primary_topic['name'] != group['target_name']:
                print(f"  📝 Renaming '{primary_topic['name']}' to '{group['target_name']}'")
            
            # List topics that would be merged
            topics_to_merge = [t for t in group['topics'] if t['id'] != primary_topic['id']]
            for topic in topics_to_merge:
                print(f"  🔗 Would merge: {topic['name']}")
            
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Error processing group {i}: {e}")
    
    print(f"\n✅ Processed {success_count}/{len(groups)} consolidation groups")
    return success_count

def run_server_cleanup():
    """Run the server's built-in cleanup to help with consolidation"""
    print("\n🧹 Running server cleanup to help with consolidation...")
    
    try:
        response = requests.post('http://localhost:3001/api/cleanup-topics')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                cleanup_result = result.get('result', {})
                original_topics = cleanup_result.get('original_topics', 0)
                merged_topics = cleanup_result.get('merged_topics', 0)
                removed_count = original_topics - merged_topics
                
                print(f"  ✅ Server cleanup removed {removed_count} duplicate topics")
                print(f"  📊 Topics: {original_topics} → {merged_topics}")
                return True
            else:
                print(f"  ❌ Server cleanup failed")
                return False
        else:
            print(f"  ❌ Server cleanup error: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Error running server cleanup: {e}")
        return False

def main():
    """Main consolidation process"""
    print("🚀 Starting aggressive topic consolidation...")
    print("="*60)
    
    # Find consolidation groups
    groups = find_consolidation_groups()
    
    if not groups:
        print("✅ No consolidation groups found - topics are already well consolidated!")
        return True
    
    # Show consolidation plan
    topics_to_merge = show_consolidation_plan(groups)
    
    # Ask for confirmation
    print(f"\n❓ Proceed with consolidating {topics_to_merge} topics? (y/N): ", end="")
    response = input()
    if response.lower() != 'y':
        print("❌ Consolidation cancelled")
        return False
    
    # Run server cleanup first
    cleanup_success = run_server_cleanup()
    
    # Apply consolidations
    consolidation_success = apply_consolidations(groups)
    
    # Run cleanup again after consolidations
    if consolidation_success > 0:
        print("\n🔄 Running final cleanup...")
        run_server_cleanup()
    
    print("\n" + "="*60)
    if cleanup_success or consolidation_success > 0:
        print("✅ Aggressive topic consolidation completed!")
        print("\n💡 Next steps:")
        print("   1. Check the web interface to see consolidated topics")
        print("   2. Verify that similar topics are now merged")
        print("   3. Adjust topic visibility if needed")
    else:
        print("❌ Consolidation had issues")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Aggressive consolidation failed!")
        exit(1)
