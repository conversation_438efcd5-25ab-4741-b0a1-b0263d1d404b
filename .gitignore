# Dependencies
node_modules/
venv/
*.pyc

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.idea/
.vscode/
*.swp
*.swo

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build
dist/
build/
coverage/

# Database
*.sqlite
*.db

# AI Model
models/
*.weights
*.h5

# Temp files
.DS_Store
Thumbs.db

# Security
*.pem
*.key
*.cert

# Config
config.json
settings.json
!example.config.json
!default.settings.json

# Documents
documents/
*.md
*.pdf
*.docx
*.doc
.cursor/

__pycache__
venv/

# 忽略数据目录
data/

# 依赖
/client/node_modules
/server/node_modules