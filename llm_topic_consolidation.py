#!/usr/bin/env python3
"""
LLM-powered topic consolidation script
Uses OpenAI to intelligently identify and merge similar topics
"""

import requests
import json
import os
from openai import OpenAI
from datetime import datetime

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

def get_all_topics():
    """Fetch all topics from the API"""
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                all_topics = result.get('topics', [])
                # Separate people and topics
                people = [t for t in all_topics if t.get('category') == 'people']
                topics = [t for t in all_topics if t.get('category') != 'people']
                return topics, people
        return [], []
    except Exception as e:
        print(f"Error fetching topics: {e}")
        return [], []

def analyze_topics_with_llm(topics):
    """Use LLM to analyze and group similar topics"""

    # Process topics in smaller batches to avoid token limits
    batch_size = 50  # Process 50 topics at a time
    all_consolidation_groups = []

    for i in range(0, len(topics), batch_size):
        batch_topics = topics[i:i + batch_size]
        print(f"🔍 Analyzing batch {i//batch_size + 1}/{(len(topics) + batch_size - 1)//batch_size} ({len(batch_topics)} topics)...")

        # Prepare topic list for LLM analysis
        topic_list = []
        for topic in batch_topics:
            topic_info = {
                'id': topic['id'],
                'name': topic['name'],
                'category': topic.get('category', 'unknown'),
                'keywords': topic.get('keywords', [])
            }
            topic_list.append(topic_info)

        batch_groups = analyze_topic_batch_with_llm(topic_list)
        all_consolidation_groups.extend(batch_groups)

    return all_consolidation_groups

def analyze_topic_batch_with_llm(topic_list):
    """Analyze a batch of topics with LLM"""

    # Create prompt for LLM
    prompt = f"""
You are an expert at organizing and consolidating topics. I have a list of {len(topic_list)} topics from a diary application that need to be consolidated because there are many duplicates and very similar topics.

CONSOLIDATION RULES:
1. Topics should be SMALL and GENERIC
2. Similar topics should be merged into the same consolidated topic
3. Focus on the CORE CONCEPT, not specific implementations or variations
4. Examples of good consolidation:
   - "AI日记", "AI语言工具", "AI口语工具", "AI工具" → "AI工具"
   - "AI口语tutor后端", "AI口语Tutor项目", "AI口语项目", "AI外语口语工具" → "AI口语"
   - "智能OA系统调研", "智能OA系统原型", "OA系统" → "智能OA系统"
   - "菜地修整", "菜地规划", "菜地除草" → "菜地"

TOPICS TO ANALYZE:
{json.dumps(topic_list, ensure_ascii=False, indent=2)}

Please analyze these topics and return a JSON response with consolidation groups. Each group should have:
- "consolidated_name": The best generic name for the group
- "topic_ids": List of topic IDs that should be merged into this group
- "reason": Brief explanation of why these topics belong together

Only group topics that are truly similar or related. Don't force unrelated topics together.

Return ONLY valid JSON in this format:
{{
  "consolidation_groups": [
    {{
      "consolidated_name": "AI工具",
      "topic_ids": ["topic_id_1", "topic_id_2", "topic_id_3"],
      "reason": "All are AI-related tools and applications"
    }}
  ]
}}
"""

    try:
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are an expert at topic organization and consolidation. Always return valid JSON."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.1
        )
        
        result_text = response.choices[0].message.content.strip()
        
        # Try to extract JSON from the response
        if result_text.startswith('```json'):
            result_text = result_text[7:-3]
        elif result_text.startswith('```'):
            result_text = result_text[3:-3]
        
        result = json.loads(result_text)
        return result.get('consolidation_groups', [])
        
    except Exception as e:
        print(f"Error with LLM analysis: {e}")
        return []

def apply_consolidation_groups(topics, consolidation_groups):
    """Apply the consolidation groups to create new topic structure"""
    
    # Create mapping of topic ID to topic
    topic_map = {topic['id']: topic for topic in topics}
    
    # Track which topics have been consolidated
    consolidated_topic_ids = set()
    new_topics = []
    
    print(f"\n🔄 Applying {len(consolidation_groups)} consolidation groups...")
    
    for group in consolidation_groups:
        consolidated_name = group['consolidated_name']
        topic_ids = group['topic_ids']
        reason = group.get('reason', '')
        
        # Validate that all topic IDs exist
        valid_topic_ids = [tid for tid in topic_ids if tid in topic_map]
        if len(valid_topic_ids) < 2:
            continue  # Skip groups with less than 2 valid topics
        
        print(f"\n🔗 Consolidating {len(valid_topic_ids)} topics into '{consolidated_name}':")
        print(f"   Reason: {reason}")
        
        # Use the first topic as the base and update it
        primary_topic_id = valid_topic_ids[0]
        primary_topic = topic_map[primary_topic_id].copy()
        
        # Update the primary topic
        primary_topic['name'] = consolidated_name
        
        # Merge keywords from all topics in the group
        all_keywords = set(primary_topic.get('keywords', []))
        for topic_id in valid_topic_ids:
            topic = topic_map[topic_id]
            print(f"   - {topic['name']}")
            all_keywords.update(topic.get('keywords', []))
            consolidated_topic_ids.add(topic_id)
        
        primary_topic['keywords'] = list(all_keywords)
        primary_topic['context'] = f"从日记中提取的{primary_topic.get('type', 'topic')}: {consolidated_name}"
        
        new_topics.append(primary_topic)
    
    # Add topics that weren't consolidated
    for topic in topics:
        if topic['id'] not in consolidated_topic_ids:
            new_topics.append(topic)
    
    print(f"\n✨ Consolidation complete:")
    print(f"   Before: {len(topics)} topics")
    print(f"   After: {len(new_topics)} topics")
    print(f"   Removed: {len(topics) - len(new_topics)} duplicate topics")
    
    return new_topics

def backup_current_state():
    """Create a backup before making changes"""
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code == 200:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"data/topics_backup_llm_{timestamp}.json"
            
            os.makedirs('data', exist_ok=True)
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(response.json(), f, ensure_ascii=False, indent=2)
            
            print(f"💾 Backup saved to {backup_filename}")
            return backup_filename
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
    return None

def show_consolidation_preview(consolidation_groups, topics):
    """Show a preview of what will be consolidated"""
    topic_map = {topic['id']: topic for topic in topics}
    
    print(f"\n📋 Consolidation Preview ({len(consolidation_groups)} groups):")
    print("="*60)
    
    for i, group in enumerate(consolidation_groups, 1):
        consolidated_name = group['consolidated_name']
        topic_ids = group['topic_ids']
        reason = group.get('reason', '')
        
        valid_topics = [topic_map[tid] for tid in topic_ids if tid in topic_map]
        if len(valid_topics) < 2:
            continue
        
        print(f"\n{i}. '{consolidated_name}' ({len(valid_topics)} topics)")
        print(f"   Reason: {reason}")
        for topic in valid_topics:
            print(f"   - {topic['name']}")
    
    total_to_remove = sum(len([tid for tid in group['topic_ids'] if tid in topic_map]) - 1 
                         for group in consolidation_groups 
                         if len([tid for tid in group['topic_ids'] if tid in topic_map]) >= 2)
    
    print(f"\n📊 Summary: Will remove {total_to_remove} duplicate topics")

def main():
    """Main consolidation process"""
    print("🚀 Starting LLM-powered topic consolidation...")
    print("="*60)
    
    # Check if OpenAI API key is available
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OpenAI API key not found. Please set OPENAI_API_KEY environment variable.")
        return False
    
    # Fetch current topics
    topics, people = get_all_topics()
    if not topics:
        print("❌ No topics found to consolidate")
        return False
    
    print(f"📊 Found {len(topics)} topics and {len(people)} people")
    
    # Create backup
    backup_file = backup_current_state()
    if not backup_file:
        print("⚠️  Could not create backup, but continuing...")
    
    # Analyze topics with LLM
    print(f"\n🤖 Analyzing topics with LLM...")
    consolidation_groups = analyze_topics_with_llm(topics)
    
    if not consolidation_groups:
        print("❌ LLM analysis failed or found no consolidations")
        return False
    
    # Show preview
    show_consolidation_preview(consolidation_groups, topics)
    
    # Ask for confirmation
    print(f"\n❓ Apply these consolidations? (y/N): ", end="")
    response = input()
    if response.lower() != 'y':
        print("❌ Consolidation cancelled")
        return False
    
    # Apply consolidations
    new_topics = apply_consolidation_groups(topics, consolidation_groups)
    
    # Note: In a real implementation, you would need API endpoints to update the topics
    # For now, we'll save the consolidated topics to a file
    output_file = f"data/consolidated_topics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    consolidated_data = {
        'topics': new_topics,
        'people': people,
        'consolidation_applied': True,
        'original_count': len(topics),
        'consolidated_count': len(new_topics)
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(consolidated_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n✅ Consolidation completed!")
    print(f"📁 Results saved to: {output_file}")
    print(f"📁 Backup saved to: {backup_file}")
    
    print(f"\n💡 Next steps:")
    print(f"   1. Review the consolidated topics in {output_file}")
    print(f"   2. Implement API endpoints to apply these changes")
    print(f"   3. Test the consolidated topics in the web interface")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ LLM topic consolidation failed!")
        exit(1)
