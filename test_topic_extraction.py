#!/usr/bin/env python3
"""
Test script to verify the noun-only topic extraction approach
"""

import requests
import json

def test_topic_extraction():
    """Test the topic extraction endpoint with sample content"""
    
    # Sample diary content that should extract nouns only
    test_content = """
    今天我和刘健开会讨论AI项目。我们在杭州的办公室里工作，
    使用Python编程语言开发智能系统。我的猫在旁边睡觉。
    我们计划学习新的机器学习技术，并且要去北京出差。
    """
    
    print("Testing topic extraction with sample content:")
    print(f"Content: {test_content}")
    print("\n" + "="*50)
    
    try:
        # Test the extract topics endpoint
        response = requests.post(
            'http://localhost:3001/api/extract-topics',
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Topic extraction successful!")
            print(f"Status: {result.get('status', 'unknown')}")
            
            if 'topics' in result:
                print(f"\n📝 Found {len(result['topics'])} topics:")
                for topic in result['topics']:
                    print(f"  - {topic['name']} ({topic['category']}) - {topic['type']}")
                    
            if 'people' in result:
                print(f"\n👤 Found {len(result['people'])} people:")
                for person in result['people']:
                    print(f"  - {person['name']} ({person.get('role', 'unknown role')})")
                    
            if 'relations' in result:
                print(f"\n🔗 Found {len(result['relations'])} relations:")
                for relation in result['relations']:
                    print(f"  - {relation['source']} --{relation['type']}--> {relation['target']}")
                    
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Exception: {e}")

def check_current_topics():
    """Check the current topics in the system"""
    
    try:
        response = requests.get('http://localhost:3001/api/topics/visible')
        
        if response.status_code == 200:
            result = response.json()
            print("\n" + "="*50)
            print("📋 Current visible topics in system:")
            
            if result.get('status') == 'success':
                topics = result.get('topics', [])
                print(f"Found {len(topics)} visible topics:")
                
                # Group by category
                by_category = {}
                for topic in topics:
                    category = topic.get('category', 'unknown')
                    if category not in by_category:
                        by_category[category] = []
                    by_category[category].append(topic)
                
                for category, category_topics in by_category.items():
                    print(f"\n{category.upper()}:")
                    for topic in category_topics:
                        print(f"  - {topic['name']} (importance: {topic.get('importance', 'N/A')})")
            else:
                print(f"Error: {result.get('message', 'Unknown error')}")
                
        else:
            print(f"❌ Error fetching topics: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception fetching topics: {e}")

if __name__ == "__main__":
    print("🧪 Testing Topic Extraction (Noun-Only Approach)")
    print("="*60)
    
    # First check current topics
    check_current_topics()
    
    # Then test extraction
    test_topic_extraction()
    
    print("\n" + "="*60)
    print("✅ Test completed!")
