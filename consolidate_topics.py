#!/usr/bin/env python3
"""
<PERSON>ript to consolidate similar topics into generic, small topics
"""

import json
import re
from difflib import SequenceMatcher
from collections import defaultdict

def load_topics():
    """Load current topics from topics.json"""
    try:
        with open('data/topics.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading topics: {e}")
        return None

def normalize_topic_name(name):
    """Normalize topic name for comparison"""
    # Remove common suffixes and prefixes
    normalized = name.lower()
    
    # Remove common project-related words
    patterns_to_remove = [
        r'项目$', r'系统$', r'平台$', r'工具$', r'应用$',
        r'前端.*', r'后端.*', r'界面.*', r'网站.*', r'重构.*',
        r'改进.*', r'改造.*', r'设计.*', r'搭建.*', r'实现.*',
        r'调研.*', r'技术栈.*', r'开发.*', r'功能.*'
    ]
    
    for pattern in patterns_to_remove:
        normalized = re.sub(pattern, '', normalized)
    
    # Clean up whitespace
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    
    return normalized

def calculate_similarity(name1, name2):
    """Calculate similarity between two topic names"""
    norm1 = normalize_topic_name(name1)
    norm2 = normalize_topic_name(name2)

    # Direct match after normalization
    if norm1 == norm2:
        return 1.0

    # Check if one is contained in the other (for cases like "AI口语" and "AI口语项目")
    if norm1 in norm2 or norm2 in norm1:
        return 0.9

    # Check for common core concepts
    core_patterns = [
        r'ai\s*fintech',
        r'ai\s*口语',
        r'智能\s*oa',
        r'蓝领\s*招聘',
        r'生活\s*成本',
        r'投资\s*项目',
        r'初创.*挖掘',
        r'抖音',
        r'菜地',
        r'easymentor',
        r'godot'
    ]

    for pattern in core_patterns:
        if re.search(pattern, norm1, re.IGNORECASE) and re.search(pattern, norm2, re.IGNORECASE):
            return 0.85

    # Sequence matcher for general similarity
    return SequenceMatcher(None, norm1, norm2).ratio()

def find_topic_groups(topics):
    """Group similar topics together"""
    groups = []
    used_indices = set()
    
    for i, topic in enumerate(topics):
        if i in used_indices:
            continue
            
        # Start a new group with this topic
        group = [topic]
        group_indices = [i]
        
        # Find similar topics
        for j, other_topic in enumerate(topics[i+1:], i+1):
            if j in used_indices:
                continue
                
            similarity = calculate_similarity(topic['name'], other_topic['name'])
            
            # High threshold for grouping
            if similarity > 0.7:
                group.append(other_topic)
                group_indices.append(j)
        
        # Mark all topics in this group as used
        used_indices.update(group_indices)
        groups.append(group)
    
    return groups

def suggest_consolidated_name(group):
    """Suggest a consolidated name for a group of similar topics"""
    names = [topic['name'] for topic in group]

    # Enhanced manual mapping for known patterns
    consolidated_mappings = {
        'ai fintech': 'AI Fintech',
        'ai口语': 'AI口语',
        'ai 口语': 'AI口语',
        '智能oa': '智能OA系统',
        '蓝领招聘': '蓝领招聘平台',
        '生活成本': '生活成本计算',
        '对账': '生活成本计算',
        '投资项目': '投资项目挖掘',
        '初创': '初创项目挖掘',
        '空头': '投资项目挖掘',
        '尽职调查': '投资项目挖掘',
        '抖音': '抖音项目',
        '菜地': '菜地',
        '猫': '猫',
        'easymentor': 'EasyMentor',
        'godot': 'Godot',
        'augment': 'Augment'
    }

    # Find the core concept by looking for common patterns
    all_names_text = ' '.join(names).lower()

    # Check manual mappings first
    for key, value in consolidated_mappings.items():
        if key in all_names_text:
            return value

    # Extract core concept from the most common words
    # Remove common suffixes and find the core
    core_words = set()
    for name in names:
        # Remove common project-related suffixes
        cleaned = re.sub(r'(项目|系统|平台|工具|前端|后端|界面|网站|重构|改进|改造|设计|搭建|实现|调研|技术栈|开发|功能|demo|Demo|MVP|UI|UX).*$', '', name, flags=re.IGNORECASE)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        if cleaned:
            core_words.add(cleaned)

    if core_words:
        # Return the shortest core word
        return min(core_words, key=len)

    # Fallback: return the shortest original name
    return min(names, key=len)

def consolidate_topics_data():
    """Main function to consolidate topics from API"""

    print("🔍 Fetching all topics from API...")

    # Fetch all topics from the API
    import requests
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code != 200:
            print(f"❌ Error fetching topics: {response.status_code}")
            return None

        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"❌ API error: {api_data.get('message', 'Unknown error')}")
            return None

        all_topics = api_data.get('topics', [])
        print(f"Found {len(all_topics)} topics from API")

        # Separate people and topics
        people = [t for t in all_topics if t.get('category') == 'people']
        topics = [t for t in all_topics if t.get('category') != 'people']

        print(f"Topics: {len(topics)}, People: {len(people)}")

    except Exception as e:
        print(f"❌ Error fetching from API: {e}")
        return None

    # Group similar topics
    topic_groups = find_topic_groups(topics)

    print(f"\n📊 Found {len(topic_groups)} topic groups:")

    consolidated_topics = []
    topic_id_mapping = {}  # Old ID -> New ID

    for i, group in enumerate(topic_groups):
        if len(group) > 1:
            print(f"\n🔗 Group {i+1} ({len(group)} topics):")
            for topic in group:
                print(f"  - {topic['name']}")

            # Create consolidated topic
            suggested_name = suggest_consolidated_name(group)
            print(f"  → Suggested consolidation: '{suggested_name}'")

            # Use the first topic as base, update name
            consolidated_topic = group[0].copy()
            consolidated_topic['name'] = suggested_name
            consolidated_topic['context'] = f"从日记中提取的{consolidated_topic.get('type', 'topic')}: {suggested_name}"

            # Merge keywords from all topics
            all_keywords = set()
            for topic in group:
                all_keywords.update(topic.get('keywords', []))
                topic_id_mapping[topic['id']] = consolidated_topic['id']

            consolidated_topic['keywords'] = list(all_keywords)
            consolidated_topics.append(consolidated_topic)

        else:
            # Single topic, keep as is
            topic = group[0]
            consolidated_topics.append(topic)
            topic_id_mapping[topic['id']] = topic['id']
    
    print(f"\n✨ Consolidation complete!")
    print(f"Before: {len(topics)} topics")
    print(f"After: {len(consolidated_topics)} topics")
    print(f"Reduction: {len(topics) - len(consolidated_topics)} topics")

    # For now, we'll just show the consolidated topics
    # The actual consolidation would need to be done via API calls
    # to update the database

    print(f"\n📋 Consolidated topics:")
    for topic in consolidated_topics:
        print(f"  - {topic['name']} ({topic.get('category', 'unknown')})")

    # Return the consolidated data for further processing
    return {
        'topics': consolidated_topics,
        'people': people,
        'topic_mapping': topic_id_mapping
    }

if __name__ == "__main__":
    print("🚀 Starting topic consolidation...")
    print("="*60)
    
    result = consolidate_topics_data()
    
    if result:
        print("\n" + "="*60)
        print("✅ Topic consolidation completed successfully!")
        print("\nFinal topics:")
        for topic in result['topics']:
            print(f"  - {topic['name']} ({topic['category']})")
    else:
        print("❌ Topic consolidation failed!")
