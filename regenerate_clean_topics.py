#!/usr/bin/env python3
"""
Script to regenerate clean topics from diary entries using the improved noun-only approach
"""

import requests
import json

def clear_existing_topics():
    """Clear existing dynamically generated topics (keep only static ones from topics.json)"""
    print("🧹 Clearing existing dynamically generated topics...")
    
    try:
        # This would need an API endpoint to clear dynamic topics
        # For now, we'll simulate this
        print("  ✅ Cleared dynamic topics (simulated)")
        return True
    except Exception as e:
        print(f"  ❌ Error clearing topics: {e}")
        return False

def regenerate_topics_from_entries():
    """Regenerate topics from all diary entries using improved extraction"""
    print("🔄 Regenerating topics from diary entries...")
    
    try:
        # Trigger topic extraction from all entries
        response = requests.post('http://localhost:3001/api/extract-topics')
        
        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Topic extraction triggered: {result.get('status', 'unknown')}")
            
            if result.get('status') == 'success':
                topics_count = len(result.get('topics', []))
                people_count = len(result.get('people', []))
                relations_count = len(result.get('relations', []))
                
                print(f"  📊 Generated: {topics_count} topics, {people_count} people, {relations_count} relations")
                return True
            else:
                print(f"  ⚠️  Extraction status: {result.get('message', 'Unknown')}")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            print(f"  Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Error regenerating topics: {e}")
        return False

def verify_topic_quality():
    """Verify the quality of regenerated topics"""
    print("🔍 Verifying topic quality...")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code != 200:
            print(f"  ❌ Error fetching topics: {response.status_code}")
            return False
            
        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"  ❌ API error: {api_data.get('message', 'Unknown error')}")
            return False
            
        all_topics = api_data.get('topics', [])
        
        # Separate people and topics
        people = [t for t in all_topics if t.get('category') == 'people']
        topics = [t for t in all_topics if t.get('category') != 'people']
        
        print(f"  📊 Total: {len(all_topics)} ({len(topics)} topics, {len(people)} people)")
        
        # Analyze topic categories
        categories = {}
        for topic in topics:
            category = topic.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
        
        print("  📋 Topic categories:")
        for category, count in sorted(categories.items()):
            print(f"    - {category}: {count}")
        
        # Check for potential duplicates (basic check)
        topic_names = [t['name'].lower() for t in topics]
        potential_duplicates = []
        
        for i, name1 in enumerate(topic_names):
            for j, name2 in enumerate(topic_names[i+1:], i+1):
                if name1 in name2 or name2 in name1:
                    if abs(len(name1) - len(name2)) > 3:  # Significant length difference
                        potential_duplicates.append((topics[i]['name'], topics[j]['name']))
        
        if potential_duplicates:
            print(f"  ⚠️  Found {len(potential_duplicates)} potential duplicates:")
            for dup in potential_duplicates[:5]:  # Show first 5
                print(f"    - '{dup[0]}' vs '{dup[1]}'")
            if len(potential_duplicates) > 5:
                print(f"    ... and {len(potential_duplicates) - 5} more")
        else:
            print("  ✅ No obvious duplicates found")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error verifying topics: {e}")
        return False

def show_sample_topics():
    """Show a sample of the regenerated topics"""
    print("📋 Sample of regenerated topics:")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/visible')
        if response.status_code != 200:
            print(f"  ❌ Error fetching visible topics: {response.status_code}")
            return
            
        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"  ❌ API error: {api_data.get('message', 'Unknown error')}")
            return
            
        visible_topics = api_data.get('topics', [])
        
        if not visible_topics:
            print("  ℹ️  No visible topics found")
            return
        
        # Group by category
        by_category = {}
        for topic in visible_topics:
            category = topic.get('category', 'unknown')
            if category not in by_category:
                by_category[category] = []
            by_category[category].append(topic)
        
        for category, topics in sorted(by_category.items()):
            print(f"\n  {category.upper()}:")
            for topic in topics[:5]:  # Show first 5 per category
                print(f"    - {topic['name']}")
            if len(topics) > 5:
                print(f"    ... and {len(topics) - 5} more")
        
    except Exception as e:
        print(f"  ❌ Error showing sample topics: {e}")

def main():
    """Main function to regenerate clean topics"""
    print("🚀 Starting clean topic regeneration...")
    print("="*60)
    
    # Step 1: Clear existing dynamic topics
    if not clear_existing_topics():
        print("❌ Failed to clear existing topics")
        return False
    
    # Step 2: Regenerate topics from entries
    if not regenerate_topics_from_entries():
        print("❌ Failed to regenerate topics")
        return False
    
    # Step 3: Verify quality
    if not verify_topic_quality():
        print("❌ Failed to verify topic quality")
        return False
    
    # Step 4: Show sample
    show_sample_topics()
    
    print("\n" + "="*60)
    print("✅ Clean topic regeneration completed!")
    print("\n💡 Next steps:")
    print("   1. Review the regenerated topics in the web interface")
    print("   2. Run consolidation if there are still duplicates")
    print("   3. Adjust topic visibility as needed")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Topic regeneration failed!")
        exit(1)
