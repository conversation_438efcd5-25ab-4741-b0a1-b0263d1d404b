{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@apollo/client": "^3.13.8", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "d3": "^7.9.0", "graphql": "^16.11.0", "graphql-request": "^7.2.0", "react": "^19.1.0", "react-calendar": "^5.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "tyme4ts": "^1.3.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "check-tyme": "npm list tyme4ts"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}