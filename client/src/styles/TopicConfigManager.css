.topic-config-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background-color: #fff;
  border-radius: 0;
  box-shadow: none;
  height: 100%;
  overflow-y: auto;
}

.topic-config-header {
  text-align: center;
  margin-bottom: 30px;
}

.topic-config-header h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 8px;
}

.topic-config-header p {
  color: #666;
  font-size: 1rem;
}

.topic-config-tabs {
  display: flex;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 30px;
  gap: 10px;
}

.tab-button {
  padding: 12px 20px;
  border: none;
  background: none;
  color: #666;
  font-size: 1rem;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #ff7a5c;
  background-color: #fff5f3;
}

.tab-button.active {
  color: #ff7a5c;
  border-bottom-color: #ff7a5c;
  font-weight: 500;
}

.topic-config-content {
  min-height: 400px;
}

.topics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.topic-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
}

.topic-card.visible {
  border-color: #4caf50;
  background: #f1f8e9;
}

.topic-card.hidden {
  border-color: #ff9800;
  background: #fff3e0;
  opacity: 0.7;
}

.topic-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.topic-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.topic-icon {
  font-size: 1.2rem;
}

.topic-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.topic-priority {
  background: #66bb6a;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  min-width: 20px;
  text-align: center;
}

.topic-category {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 12px;
  text-transform: capitalize;
}

.topic-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.priority-select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.8rem;
  flex: 1;
}

.hide-button, .show-button, .delete-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.hide-button {
  background: #ff9800;
  color: white;
}

.hide-button:hover {
  background: #f57c00;
}

.show-button {
  background: #4caf50;
  color: white;
}

.show-button:hover {
  background: #388e3c;
}

.delete-button {
  background: #f44336;
  color: white;
}

.delete-button:hover {
  background: #d32f2f;
}

.custom-topic-form {
  max-width: 500px;
  margin: 0 auto;
  background: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #ff7a5c;
  box-shadow: 0 0 0 2px rgba(255, 122, 92, 0.2);
}

.create-button {
  width: 100%;
  padding: 12px;
  background: #ff7a5c;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.create-button:hover {
  background: #ff6b4a;
}

.settings-form {
  max-width: 600px;
  margin: 0 auto;
}

.setting-group {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.setting-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.setting-group input[type="checkbox"] {
  width: auto;
}

.setting-group input[type="number"],
.setting-group select {
  width: 100%;
  max-width: 200px;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.topic-config-loading,
.topic-config-error {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 1.1rem;
  color: #666;
}

.topic-config-error {
  color: #f44336;
  background: #ffebee;
  border-radius: 8px;
  margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .topic-config-manager {
    padding: 15px;
  }
  
  .topics-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .topic-config-tabs {
    flex-wrap: wrap;
    gap: 5px;
  }
  
  .tab-button {
    padding: 10px 15px;
    font-size: 0.9rem;
  }
  
  .custom-topic-form {
    padding: 20px;
  }
}

/* Animation for topic cards */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.topic-card {
  animation: slideIn 0.3s ease-out;
}

/* Status indicators */
.topic-card::before {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ccc;
}

.topic-card.visible::before {
  background: #4caf50;
}

.topic-card.hidden::before {
  background: #ff9800;
}

.topic-card {
  position: relative;
}

.topic-card.selected {
  border-color: #2196f3;
  background: #e3f2fd;
  transform: translateY(-1px);
}

/* Enhanced Topic Management Styles */
.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 16px;
}

.topic-controls {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  width: 200px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #ff7a5c;
  box-shadow: 0 0 0 2px rgba(255, 122, 92, 0.2);
}

.category-filter, .sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
}

.bulk-actions {
  background: #f0f8ff;
  border: 1px solid #2196f3;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.selection-count {
  font-weight: 500;
  color: #2196f3;
}

.bulk-buttons {
  display: flex;
  gap: 8px;
}

.bulk-show-button, .bulk-hide-button, .clear-selection-button {
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bulk-show-button {
  background: #4caf50;
  color: white;
}

.bulk-show-button:hover {
  background: #388e3c;
}

.bulk-hide-button {
  background: #ff9800;
  color: white;
}

.bulk-hide-button:hover {
  background: #f57c00;
}

.clear-selection-button {
  background: #9e9e9e;
  color: white;
}

.clear-selection-button:hover {
  background: #757575;
}

.topic-actions-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.select-all-button {
  background: #2196f3;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.select-all-button:hover {
  background: #1976d2;
}

.topic-count {
  color: #666;
  font-size: 0.9rem;
}

.topic-checkbox {
  margin-right: 8px;
  cursor: pointer;
}

.topic-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.topic-usage {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.topic-type {
  background: #fff3e0;
  color: #f57c00;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.visibility-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 500;
}

.visibility-badge.visible {
  background: #e8f5e8;
  color: #2e7d32;
}

.visibility-badge.hidden {
  background: #ffebee;
  color: #c62828;
}

/* Analytics Tab Styles */
.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.refresh-analytics-button {
  background: #2196f3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.refresh-analytics-button:hover:not(:disabled) {
  background: #1976d2;
}

.refresh-analytics-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.analytics-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.analytics-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.analytics-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e9ecef;
}

.analytics-section h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Overview Grid */
.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.overview-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-number {
  font-size: 2rem;
  font-weight: bold;
  color: #2196f3;
  margin-bottom: 8px;
}

.overview-label {
  color: #666;
  font-size: 0.9rem;
}

/* Insights */
.insights-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.insight-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border-left: 4px solid #2196f3;
  border: 1px solid #e9ecef;
}

.insight-card.most_active {
  border-left-color: #4caf50;
}

.insight-card.category_focus {
  border-left-color: #ff9800;
}

.insight-card.hidden_active {
  border-left-color: #f44336;
}

.insight-card.inactive_visible {
  border-left-color: #9e9e9e;
}

.insight-header {
  margin-bottom: 8px;
}

.insight-title {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.insight-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.insight-topics {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.insight-topic {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Trends */
.trends-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.trend-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.trend-name {
  font-weight: 500;
  color: #333;
}

.trend-direction {
  font-size: 1.2rem;
}

.trend-direction.up {
  color: #4caf50;
}

.trend-direction.down {
  color: #f44336;
}

.trend-direction.stable {
  color: #9e9e9e;
}

.trend-stats {
  display: flex;
  gap: 16px;
  align-items: center;
}

.trend-category {
  background: #f5f5f5;
  color: #666;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
}

.trend-mentions {
  color: #666;
  font-size: 0.9rem;
}

/* Recommendations */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  border-left: 4px solid #2196f3;
}

.recommendation-card.priority-high {
  border-left-color: #f44336;
}

.recommendation-card.priority-medium {
  border-left-color: #ff9800;
}

.recommendation-card.priority-low {
  border-left-color: #4caf50;
}

.recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.recommendation-title {
  font-weight: 600;
  color: #333;
}

.priority-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  color: white;
}

.priority-badge.high {
  background: #f44336;
}

.priority-badge.medium {
  background: #ff9800;
}

.priority-badge.low {
  background: #4caf50;
}

.recommendation-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 12px;
}

.topic-suggestions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.suggested-topic {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Activity Patterns */
.patterns-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.pattern-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  text-align: center;
}

.pattern-title {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.pattern-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

/* Topic Relationships */
.relationships-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.relationship-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.relationship-topics {
  display: flex;
  align-items: center;
  gap: 12px;
}

.topic-name {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 500;
}

.relationship-connector {
  font-size: 1.2rem;
  color: #666;
}

.relationship-count {
  color: #666;
  font-size: 0.9rem;
}

/* Suggestions Tab Styles */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 20px;
}

.suggestion-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 2px solid #e3f2fd;
  transition: all 0.3s ease;
}

.suggestion-card:hover {
  border-color: #2196f3;
  transform: translateY(-1px);
}

.suggestion-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.suggestion-icon {
  font-size: 1.2rem;
}

.suggestion-name {
  font-weight: 500;
  color: #333;
  flex: 1;
}

.suggestion-confidence {
  background: #2196f3;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.suggestion-details {
  margin-bottom: 12px;
  color: #666;
  font-size: 0.9rem;
}

.suggestion-details p {
  margin: 4px 0;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
}

.approve-button, .reject-button {
  padding: 6px 16px;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.approve-button {
  background: #4caf50;
  color: white;
}

.approve-button:hover {
  background: #388e3c;
}

.reject-button {
  background: #f44336;
  color: white;
}

.reject-button:hover {
  background: #d32f2f;
}

.no-suggestions {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.process-button {
  background: #ff7a5c;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  margin-top: 16px;
  transition: background 0.3s ease;
}

.process-button:hover {
  background: #ff6b4a;
}

/* Pipeline Tab Styles */
.pipeline-status {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.status-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.status-item label {
  font-weight: 500;
  color: #666;
  font-size: 0.9rem;
}

.status-item span {
  font-size: 1.1rem;
  color: #333;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  width: fit-content;
}

.status-badge.running {
  background: #4caf50;
  color: white;
}

.status-badge.idle {
  background: #9e9e9e;
  color: white;
}

.pipeline-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.trigger-button, .process-all-button, .refresh-button {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.trigger-button {
  background: #2196f3;
  color: white;
}

.trigger-button:hover:not(:disabled) {
  background: #1976d2;
}

.trigger-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.process-all-button {
  background: #ff9800;
  color: white;
}

.process-all-button:hover:not(:disabled) {
  background: #f57c00;
}

.process-all-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.refresh-button {
  background: #9c27b0;
  color: white;
}

.refresh-button:hover {
  background: #7b1fa2;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .status-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .pipeline-actions {
    flex-direction: column;
  }

  .suggestion-actions {
    flex-direction: column;
  }

  .suggestions-list {
    gap: 12px;
  }

  .tab-header {
    flex-direction: column;
    align-items: stretch;
  }

  .topic-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }

  .bulk-actions {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }

  .bulk-buttons {
    justify-content: center;
  }

  .topic-actions-bar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
    text-align: center;
  }

  .topic-meta {
    justify-content: center;
  }

  .analytics-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .overview-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .patterns-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .trend-card {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .trend-stats {
    justify-content: space-between;
  }

  .relationship-card {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    text-align: center;
  }

  .relationship-topics {
    justify-content: center;
  }

  .recommendation-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
