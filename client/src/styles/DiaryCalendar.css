/* Calendar Container */
.diary-calendar-container {
  width: 100%;
  margin: 0 auto;
  font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

/* Calendar Body */
.react-calendar {
  width: 100%;
  max-width: 100%;
  background: white;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 8px;
  overflow: hidden;
}

/* Calendar Navigation */
.react-calendar__navigation {
  display: flex;
  height: 40px;
  margin-bottom: 10px;
}

.react-calendar__navigation button {
  min-width: 40px;
  background: none;
  border: none;
  font-size: 16px;
}

/* Week Title Style */
.react-calendar__month-view__weekdays {
  text-align: center;
  font-weight: normal;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

.react-calendar__month-view__weekdays__weekday {
  padding: 8px 0;
}

.react-calendar__month-view__weekdays__weekday abbr {
  text-decoration: none;
  border: none;
}

/* Weekend Color */
.react-calendar__month-view__weekdays__weekday:first-child abbr,
.react-calendar__month-view__weekdays__weekday:last-child abbr {
  color: #ff3366;
}

/* Date Cell Style */
.react-calendar__tile {
  padding: 0 !important;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  position: relative;
}

/* Custom Cell - Hide Original Date Number */
.custom-tile abbr {
  display: none !important;
}

/* Date Content Container */
.tile-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 3px 0;
  position: relative;
  box-sizing: border-box;
  border-radius: 4px;
}

/* Solar Date Style */
.solar-day {
  font-size: 14px;
  font-weight: normal;
  margin: 0;
  padding-top: 4px;
  text-align: center;
  display: block;
  color: #000;
  height: 22px;
  line-height: 22px;
}

/* Lunar Info Style */
.lunar-info {
  font-size: 11px;
  color: #666;
  text-align: center;
  height: 16px;
  line-height: 16px;
  display: block;
  margin: 14px 0;
  padding: 0;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Today Marker */
.today-marker {
  position: absolute;
  top: 5px;
  left: 5px;
  color: #ff7a5c;
  font-size: 11px;
  font-weight: bold;
  width: 14px;
  height: 14px;
  line-height: 14px;
  text-align: center;
}

/* Diary Entry Dot */
.diary-entry-dot {
  width: 4px;
  height: 4px;
  background-color: #ff7a5c;
  border-radius: 50%;
  position: absolute !important;
  top: 5px !important;
  right: 5px !important;
  transform: none !important;
  left: auto !important;
  bottom: auto !important;
}

/* Today's Diary Entry Dot - also in top right, just needs to avoid today marker */
.today .diary-entry-dot {
  top: 5px !important;
  right: 15px !important;
}

/* Weekend Style */
.weekend .solar-day {
  color: #ff3366;
}

/* Other Month Date Style */
.other-month .solar-day,
.other-month .lunar-info {
  color: #cccccc;
}

/* Other Month Weekend Style */
.other-month.weekend .solar-day {
  color: #ffb3c3;
}

.other-month .diary-entry-dot {
  background-color: #dddddd;
}

/* Selected Date Style */
.react-calendar__tile--active {
  background-color: #ff7a5c !important;
  color: white !important;
}

.react-calendar__tile--active .solar-day,
.react-calendar__tile--active .lunar-info {
  color: white !important;
}

.react-calendar__tile--active .diary-entry-dot {
  background-color: white;
}

/* Selected Date Info Display */
.selected-date-info {
  margin-top: 15px;
  text-align: center;
  font-weight: bold;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

/* Today Date Style */
.react-calendar__tile--now:not(.react-calendar__tile--active) {
  background-color: #ff7a5c !important;
  color: white !important;
}

/* Holiday and Solar Term Style */
.holiday .solar-day {
  color: #ff3366;
}

.holiday .lunar-info {
  color: #ff3366;
  background-color: #ffebee;
  padding: 1px 4px;
  border-radius: 2px;
}

.solar-terms .lunar-info {
  color: #ff9800;
}

/* Today (current day) styling */
.today-tile {
  background-color: #ff7a5c !important;
  color: white !important;
}

.today-tile .solar-day,
.today-tile .lunar-info {
  color: white !important;
}

.selected-tile .solar-day,
.selected-tile .lunar-info {
  color: white !important;
}

/* Override react-calendar default styles */
.react-calendar__tile--now:not(.react-calendar__tile--active) {
  background-color: #ff7a5c !important;
  color: white !important;
}

.react-calendar__tile--active:not(.react-calendar__tile--now) {
  background-color: #ffbdb1 !important;
  color: white !important;
}

/* Weekend dates within current month */
.react-calendar__month-view__days__day--weekend:not(.react-calendar__month-view__days__day--neighboringMonth) .solar-day {
  color: #ff7a5c !important;
}

/* Weekend dates in neighboring months */
.react-calendar__month-view__days__day--weekend.react-calendar__month-view__days__day--neighboringMonth .solar-day {
  color: #ffbdb1 !important;
}

/* Ensure all date cells have consistent sizing */
.react-calendar__tile {
  height: 66px;
  padding: 2px !important;
  background: none;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  font-size: inherit !important;
  font-weight: normal !important;
}

/* Ensure consistent font size for all dates */
.solar-day {
  font-size: 16px;
  font-weight: normal;
  margin-bottom: 4px;
  text-align: center;
}

/* Lunar Test Result */
.lunar-test {
  background-color: #f5f5f5;
  padding: 5px;
  border-radius: 4px;
  margin-bottom: 10px;
  font-family: monospace;
}

/* Almanac Detail Container */
.day-detail-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-top: 20px;
  text-align: left;
  font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
}

.day-detail-header {
  text-align: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.solar-date {
  font-size: 16px;
  color: #666;
  margin-bottom: 5px;
}

.lunar-date-main {
  font-size: 24px;
  color: #e53935;
  font-weight: bold;
  margin: 10px 0;
}

.lunar-ganzhi {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

/* Almanac Result */
.day-almanac-result {
  text-align: center;
  margin: 15px 0;
}

.almanac-title {
  font-size: 18px;
  color: #e53935;
  font-weight: bold;
}

/* Yi and Ji Style */
.day-yi-ji {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin-bottom: 20px;
}

.yi-section, .ji-section {
  display: flex;
  align-items: flex-start;
}

.yi-icon, .ji-icon {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-weight: bold;
  margin-right: 15px;
  font-size: 20px;
  flex-shrink: 0;
}

.yi-icon {
  background-color: #4CAF50;
}

.ji-icon {
  background-color: #F44336;
}

.yi-content, .ji-content {
  flex: 1;
  line-height: 1.8;
  font-size: 16px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.no-yiji {
  text-align: center;
  color: #999;
  margin: 20px 0;
}

.calendar-hint {
  text-align: center;
  color: #999;
  font-size: 14px;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.day-detail-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

/* More specific selector for lunar info */
.diary-calendar-container .react-calendar__tile .tile-content .lunar-info {
  font-size: 12px;
  color: #666;
  text-align: center;
  min-height: 16px;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  line-height: 1;
  margin-top: 0;
}

/* Inline Solar Term Style */
.solar-term-inline {
  color: #ff7a5c !important;
  padding: 0 5px;
  font-size: 14px;
  font-weight: bold;
}

/* Solar Term in Calendar Tile */
.tile-solar-term {
  font-size: 10px;
  color: #ff7a5c;
  position: absolute;
  bottom: 3px;
  text-align: center;
  width: 100%;
  font-weight: bold;
  height: 14px;
  line-height: 14px;
}

/* Fix for entries with solar terms */
.has-solar-term .lunar-info {
  margin-bottom: 0;
}

/* For any hard-coded blue colors that might still be present */
.solar-term-inline {
  color: #ff7a5c !important;
}

.debug-info {
  display: none;
}

