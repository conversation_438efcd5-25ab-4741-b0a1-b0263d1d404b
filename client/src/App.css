/* Core App Layout */
.App {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  font-family: "PingFang SC", "Helvetica Neue", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Arial, sans-serif;
  color: #333;
  background-color: #fcf3e8;
  min-height: 100vh;
}

/* Header */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #fff;
  padding: 0.75rem 2rem;
  border-bottom: 1px solid #eaeaea;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.logo-nav {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #ff7a5c;
  margin-right: 2rem;
}

.logo-icon {
  margin-right: 8px;
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 1.25rem;
  letter-spacing: 0.5px;
}

.app-nav {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: #666;
  text-decoration: none;
  font-size: 1rem;
  padding: 0.5rem 0;
  font-weight: 700;
}

.nav-link.active {
  color: #ff7a5c;
  font-weight: 700;
}

.new-project-button {
  background-color: #ff7a5c;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 400;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.new-project-button:hover {
  background-color: #ff6347;
}

.plus-icon {
  font-weight: normal;
  font-size: 1rem;
  line-height: 1;
}

/* Main Layout */
.app-container {
  display: flex;
  min-height: calc(100vh - 60px);
  background-color: #fcf3e8;
}

.content-container {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  order: 1;
}

.calendar-container {
  flex: 0 0 400px;
  padding: 1.5rem;
  background-color: #fcf3e8;
  border-left: 1px solid #eaeaea;
  overflow-y: auto;
  order: 2;
}

.calendar-title {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.content-inner {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.page-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
}

/* Input card styling */
.input-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

#edit-textarea {
  width: 100%;
  min-height: 300px;
  padding: 1rem;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  resize: vertical;
  font-size: 1rem;
  line-height: 1.5;
  transition: border-color 0.2s;
  box-sizing: border-box;
  font-family: inherit;
}

.diary-textarea {
  width: 100%;
  min-height: 80px;
  padding: 1rem;
  border: 1px solid #eaeaea;
  border-radius: 8px;
  resize: vertical;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 1rem;
  transition: border-color 0.2s;
  box-sizing: border-box;
  font-family: inherit;
}

.diary-textarea:focus {
  outline: none;
  border-color: #ff7a5c;
  box-shadow: 0 0 0 2px rgba(255, 122, 92, 0.2);
}

.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  box-sizing: border-box;
}

.toolbar-icons {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  color: #666;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-button:hover {
  background-color: #f5f5f5;
}

.save-button {
  padding: 0.5rem 1rem;
  background-color: #ff7a5c;
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 400;
}

.save-button:hover {
  background-color: #ff6347;
}

.save-button:disabled {
  background-color: #ffcec4;
  cursor: not-allowed;
}

.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.cancel-button {
  padding: 0.5rem 1rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #d1d5db;
  border-radius: 20px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.2s;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

/* Themes section styling */
.themes-section {
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.themes-section h2 {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 700;
}

.theme-display {
  width: 100%;
}

.theme-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 4px;
}

.theme-count {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.theme-quick-actions {
  display: flex;
  gap: 6px;
}

.quick-action-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 6px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  color: #666;
}

.quick-action-button:hover {
  background: #f5f5f5;
  border-color: #ff7a5c;
  color: #ff7a5c;
}

.theme-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.theme-tag {
  background-color: #f5f5f5;
  color: #555;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 6px;
  position: relative;
  border: 2px solid transparent;
}

.theme-tag:hover {
  background-color: #ffe5e0;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.theme-tag.active {
  background-color: #ff7a5c;
  color: white;
  border-color: #ff6b4a;
}

.theme-icon {
  font-size: 0.8rem;
  opacity: 0.8;
}

.theme-name {
  flex: 1;
}

.theme-priority {
  font-size: 0.7rem;
  opacity: 0.9;
}

.add-theme-button {
  background: none;
  border: 1px dashed #ccc;
  color: #666;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 400;
}

.add-theme-button:hover {
  border-color: #999;
  color: #333;
}

/* Theme related entries styling */
.theme-related-entries {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eaeaea;
}

.theme-related-entries h3 {
  font-size: 1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #333;
}

.theme-entries-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.theme-entry-item {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #eaeaea;
}

.theme-entry-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.theme-entry-header h4 {
  display: none;
}

.theme-entry-date {
  color: #f44336;
  font-size: 0.875rem;
  font-weight: normal;
}

.theme-entry-excerpt {
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1.5;
}

.theme-entry-excerpt .highlight {
  background-color: #fff2f0;
  color: #ff7a5c;
  padding: 0 0.25rem;
  border-radius: 3px;
  font-weight: 500;
}

.theme-entry-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 0.75rem;
}

.view-details-button {
  background: none;
  border: 1px solid #ff7a5c;
  color: #ff7a5c;
  border-radius: 20px;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.view-details-button:hover {
  background-color: #ffe7e3;
}

.view-all-container {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
}

.view-all-button {
  background-color: #ffe7e3;
  color: #ff7a5c;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.view-all-button:hover {
  background-color: #ffe5e0;
}

/* Edit form styling */
.edit-form {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.edit-header {
  margin-bottom: 1rem;
  border-bottom: 2px solid #ff7a5c;
  padding-bottom: 0.5rem;
}

.edit-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
}

.markdown-toolbar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 6px;
}

.markdown-toolbar button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.markdown-toolbar button:hover {
  background-color: #e5e7eb;
}

.edit-container {
  display: flex;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.edit-pane, .preview-pane {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 300px;
}

.markdown-preview {
  border: 1px solid #eaeaea;
  border-radius: 8px;
  padding: 1rem;
  min-height: 300px;
  height: 100%;
  background-color: #f9f9f9;
  overflow-y: auto;
  line-height: 1.6;
  box-sizing: border-box;
}

.markdown-preview h1, 
.markdown-preview h2, 
.markdown-preview h3, 
.markdown-preview h4 {
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.markdown-preview ul {
  padding-left: 1.5rem;
}

.markdown-preview p, 
.markdown-preview ul, 
.markdown-preview ol {
  margin-bottom: 1rem;
}

.markdown-preview h2 {
  font-size: 1.5rem;
  border-bottom: 1px solid #eaeaea;
  padding-bottom: 0.5rem;
}

.markdown-preview em {
  font-style: italic;
}

.markdown-preview strong {
  font-weight: bold;
}

/* Calendar styling */
.diary-calendar-container {
  margin-bottom: 1.5rem;
}

/* Day with entries indicator */
.diary-entry-dot {
  position: absolute;
  bottom: 3px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #ff7a5c;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
  }
  
  .calendar-container {
    order: 1;
    flex: none;
    width: 100%;
    border-left: none;
    border-bottom: 1px solid #eaeaea;
  }
  
  .content-container {
    order: 2;
  }
  
  .edit-container {
    flex-direction: column;
  }
  
  .content-inner {
    padding: 1rem;
  }
  
  .app-header {
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
  }
  
  .app-nav {
    gap: 1rem;
  }
}

.diary-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* Selected Date Entry Styles */
.selected-date-entry {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 20px;
}

.selected-date-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.selected-date {
  color: #ff7a5c;
  font-size: 1.2rem;
  font-weight: 500;
  margin-bottom: 4px;
  font-weight: bold;
}

.selected-type {
  font-size: 1rem;
  color: #666;
}

.entry-content-list {
  display: flex;
  flex-direction: column;
}

.entry-content-item {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 1.25rem;
  margin-bottom: 15px;
  border: 1px solid #eaeaea;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.entry-content-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}

.entry-content {
  line-height: 1.6;
  color: #333;
  white-space: pre-line;
  font-size: 0.95rem;
  padding: 5px;
}

.no-entry-message {
  color: #999;
  font-size: 0.95rem;
  padding: 10px 0;
  text-align: center;
  font-style: italic;
}

.entry-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.edit-button {
  background-color: transparent;
  border: 1px solid #ff7a5c;
  color: #ff7a5c;
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
}

.edit-button:hover {
  background-color: #ff7a5c;
  color: white;
}

/* Import dialog styles */
.import-dialog {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.import-dialog p {
  margin: 0;
  color: #555;
}

.import-dialog input[type="file"] {
  padding: 10px;
  border: 1px dashed #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.import-button {
  background-color: #fcedeb;
  color: #ff7a5c;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.import-button:hover {
  background-color: #ffe5e0;
}

.close-button {
  background-color: #eee;
  color: #555;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  align-self: flex-end;
  transition: all 0.2s;
}

.close-button:hover {
  background-color: #ddd;
}

.icon-button svg path {
  fill: #ff7a5c;
}

.almanac-more {
  text-align: right;
  margin-top: 1rem;
}

.almanac-more a {
  color: #ff7a5c;
  text-decoration: none;
  font-size: 0.9rem;
}

.themes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.themes-actions {
  display: flex;
  gap: 1rem;
}

.view-toggle-button {
  padding: 0.5rem 1rem;
  background-color: #f1f5f9;
  border: 1px solid #cbd5e0;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s;
}

.view-toggle-button:hover {
  background-color: #e2e8f0;
}

.view-toggle-button.active {
  background-color: #4299e1;
  color: white;
  border-color: #3182ce;
}

.topic-graph-wrapper {
  margin-top: 1rem;
  min-height: 500px;
  border-radius: 8px;
  overflow: hidden;
}

/* Themes header styling */
.themes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.themes-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #555;
}

/* Pagination styling */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
}

.page-button {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 50%;
  background-color: #fff;
  color: #555;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
}

.page-button:hover {
  background-color: #f5f5f5;
}

.page-button.active {
  background-color: #ff7a5c;
  color: white;
  border-color: #ff7a5c;
}

/* Entry controls styling */
.entry-controls {
  display: flex;
  align-items: center;
}

.sort-toggle-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 0.35rem 0.75rem;
  font-size: 0.8rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s;
}

.sort-toggle-button:hover {
  background-color: #eee;
}

.sort-toggle-button.active {
  background-color: #ff7a5c;
  color: white;
  border-color: #ff7a5c;
}

/* Refresh button styling */
.refresh-button {
  background-color: #ff7a5c;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.refresh-button:hover {
  background-color: #ff6347;
}

.refresh-button:disabled {
  background-color: #ffc1b6;
  cursor: not-allowed;
}

/* Loading topics styling */
.loading-topics {
  text-align: center;
  padding: 1rem;
  color: #666;
  font-style: italic;
}

/* Theme entries header styling */
.theme-entries-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.theme-sort-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 20px;
  padding: 0.35rem 0.75rem;
  font-size: 0.8rem;
  color: #555;
  cursor: pointer;
  transition: all 0.2s;
}

.theme-sort-button:hover {
  background-color: #eee;
}

.theme-sort-button.active {
  background-color: #ff7a5c;
  color: white;
  border-color: #ff7a5c;
}

.theme-pagination {
  margin-bottom: 1rem;
}

.sort-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-toggle span {
  font-size: 0.85rem;
  color: #888;
}

.sort-toggle span.active {
  color: #ff7a5c;
  font-weight: 500;
}

/* The switch - the box around the slider */
.switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

/* Hide default HTML checkbox */
.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

/* The slider */
.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: .4s;
}

input:checked + .slider {
  background-color: #ff7a5c;
}

input:focus + .slider {
  box-shadow: 0 0 1px #ff7a5c;
}

input:checked + .slider:before {
  transform: translateX(20px);
}

/* Rounded sliders */
.slider.round {
  border-radius: 20px;
}

.slider.round:before {
  border-radius: 50%;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
  font-weight: 600;
}

.modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
}

.modal-close-button:hover {
  background-color: #f5f5f5;
  color: #333;
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Responsive modal */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 10px;
  }

  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .modal-header {
    padding: 15px 20px;
  }

  .modal-header h2 {
    font-size: 1.3rem;
  }
}
