#!/usr/bin/env python3
"""
Simple script to clean up existing topics using server endpoints
"""

import requests
import json

def check_ai_status():
    """Check if AI topic extraction is enabled"""
    print("🔍 Checking AI topic extraction status...")
    
    try:
        response = requests.get('http://localhost:3001/api/ai-topics-status')
        if response.status_code == 200:
            result = response.json()
            ai_enabled = result.get('aiEnabled', False)
            print(f"  📊 AI topic extraction: {'✅ Enabled' if ai_enabled else '❌ Disabled'}")
            return ai_enabled
        else:
            print(f"  ❌ Error checking AI status: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def show_current_topic_stats():
    """Show current topic statistics"""
    print("📊 Current topic statistics...")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                all_topics = result.get('topics', [])
                
                # Separate people and topics
                people = [t for t in all_topics if t.get('category') == 'people']
                topics = [t for t in all_topics if t.get('category') != 'people']
                
                print(f"  📋 Total: {len(all_topics)} ({len(topics)} topics, {len(people)} people)")
                
                # Show category breakdown
                categories = {}
                for topic in topics:
                    category = topic.get('category', 'unknown')
                    categories[category] = categories.get(category, 0) + 1
                
                print("  📂 Categories:")
                for category, count in sorted(categories.items()):
                    print(f"    - {category}: {count}")
                
                return len(topics), len(people)
            else:
                print(f"  ❌ API error: {result.get('message', 'Unknown')}")
                return 0, 0
        else:
            print(f"  ❌ Error: {response.status_code}")
            return 0, 0
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return 0, 0

def cleanup_topics():
    """Clean up duplicate topics"""
    print("🧹 Cleaning up duplicate topics...")
    
    try:
        response = requests.post('http://localhost:3001/api/cleanup-topics')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                cleanup_result = result.get('result', {})
                original_topics = cleanup_result.get('original_topics', 0)
                merged_topics = cleanup_result.get('merged_topics', 0)
                removed_count = original_topics - merged_topics
                
                print(f"  ✅ Removed {removed_count} duplicate topics")
                print(f"  📊 Topics: {original_topics} → {merged_topics}")
                return True
            else:
                print(f"  ❌ Cleanup failed")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def deduplicate_topics():
    """Run additional deduplication"""
    print("🔧 Running topic deduplication...")
    
    try:
        response = requests.post('http://localhost:3001/api/deduplicate-topics')
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"  ✅ Deduplication completed")
                dedup_result = result.get('result', {})
                if dedup_result:
                    print(f"  📊 Results: {dedup_result}")
                return True
            else:
                print(f"  ❌ Deduplication failed: {result.get('message', 'Unknown')}")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            return False
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def show_sample_topics():
    """Show a sample of topics after cleanup"""
    print("📋 Sample topics after cleanup:")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/visible')
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                visible_topics = result.get('topics', [])
                
                if not visible_topics:
                    print("  ℹ️  No visible topics found")
                    return
                
                # Group by category and show samples
                by_category = {}
                for topic in visible_topics:
                    category = topic.get('category', 'unknown')
                    if category not in by_category:
                        by_category[category] = []
                    by_category[category].append(topic)
                
                for category, topics in sorted(by_category.items()):
                    print(f"\n  {category.upper()} ({len(topics)} topics):")
                    for topic in topics[:8]:  # Show first 8 per category
                        print(f"    - {topic['name']}")
                    if len(topics) > 8:
                        print(f"    ... and {len(topics) - 8} more")
            else:
                print(f"  ❌ Error: {result.get('message', 'Unknown')}")
        else:
            print(f"  ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"  ❌ Error: {e}")

def main():
    """Main cleanup process"""
    print("🚀 Starting simple topic cleanup...")
    print("="*50)
    
    # Check AI status
    ai_enabled = check_ai_status()
    if not ai_enabled:
        print("\n⚠️  AI topic extraction is disabled.")
        print("   Topics may not be generated with the latest improvements.")
        response = input("   Continue anyway? (y/N): ")
        if response.lower() != 'y':
            print("❌ Cleanup cancelled")
            return False
    
    # Show current stats
    print()
    topics_before, people_before = show_current_topic_stats()
    
    if topics_before == 0:
        print("\n❌ No topics found to clean up")
        return False
    
    # Ask for confirmation
    print(f"\n❓ Clean up {topics_before} topics? (y/N): ", end="")
    response = input()
    if response.lower() != 'y':
        print("❌ Cleanup cancelled")
        return False
    
    print()
    
    # Run cleanup
    cleanup_success = cleanup_topics()
    
    # Run deduplication
    dedup_success = deduplicate_topics()
    
    # Show final stats
    print()
    topics_after, people_after = show_current_topic_stats()
    
    # Show sample topics
    print()
    show_sample_topics()
    
    # Summary
    print("\n" + "="*50)
    if cleanup_success or dedup_success:
        topics_removed = topics_before - topics_after
        print("✅ Topic cleanup completed!")
        print(f"📊 Summary: {topics_before} → {topics_after} topics ({topics_removed} removed)")
        
        if topics_removed > 0:
            print(f"🎉 Successfully consolidated {topics_removed} duplicate topics!")
        else:
            print("ℹ️  No duplicates found to remove")
    else:
        print("❌ Topic cleanup had issues")
        return False
    
    print("\n💡 Next steps:")
    print("   1. Review topics in the web interface")
    print("   2. Adjust topic visibility if needed")
    print("   3. Check the topic graph")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Cleanup failed!")
        exit(1)
