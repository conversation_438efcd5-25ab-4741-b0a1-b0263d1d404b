#!/usr/bin/env python3
"""
Comprehensive script to clean up existing topics and regenerate them properly
This script will:
1. Backup current topics
2. Clear dynamic topics 
3. Regenerate with improved noun-only extraction
4. Apply consolidation if needed
"""

import requests
import json
import os
from datetime import datetime

def backup_current_topics():
    """Create a backup of current topics"""
    print("💾 Creating backup of current topics...")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code != 200:
            print(f"  ❌ Error fetching topics for backup: {response.status_code}")
            return False
            
        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"  ❌ API error: {api_data.get('message', 'Unknown error')}")
            return False
        
        # Create backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"data/topics_backup_{timestamp}.json"
        
        # Ensure data directory exists
        os.makedirs('data', exist_ok=True)
        
        # Save backup
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(api_data, f, ensure_ascii=False, indent=2)
        
        print(f"  ✅ Backup saved to {backup_filename}")
        return backup_filename
        
    except Exception as e:
        print(f"  ❌ Error creating backup: {e}")
        return False

def get_diary_entries():
    """Get all diary entries for regeneration"""
    print("📖 Fetching diary entries...")
    
    try:
        response = requests.get('http://localhost:3001/api/entries')
        if response.status_code != 200:
            print(f"  ❌ Error fetching entries: {response.status_code}")
            return None
            
        entries = response.json()
        print(f"  ✅ Found {len(entries)} diary entries")
        return entries
        
    except Exception as e:
        print(f"  ❌ Error fetching entries: {e}")
        return None

def rebuild_topics_from_scratch():
    """Use the server's rebuild-topics endpoint to rebuild all topics from scratch"""
    print("🔄 Rebuilding topics from scratch using server endpoint...")

    try:
        response = requests.post('http://localhost:3001/api/rebuild-topics')

        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Rebuild completed: {result.get('status', 'unknown')}")

            if result.get('status') == 'success':
                print(f"  📊 Results: {result.get('message', 'No details')}")
                return True
            else:
                print(f"  ⚠️  Rebuild status: {result.get('message', 'Unknown')}")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            print(f"  Response: {response.text}")
            return False

    except Exception as e:
        print(f"  ❌ Error rebuilding topics: {e}")
        return False

def cleanup_duplicate_topics():
    """Use the server's cleanup-topics endpoint to remove duplicates"""
    print("🧹 Cleaning up duplicate topics...")

    try:
        response = requests.post('http://localhost:3001/api/cleanup-topics')

        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Cleanup completed: {result.get('success', False)}")

            if result.get('success'):
                cleanup_result = result.get('result', {})
                original_topics = cleanup_result.get('original_topics', 0)
                merged_topics = cleanup_result.get('merged_topics', 0)
                removed_count = original_topics - merged_topics

                print(f"  📊 Removed {removed_count} duplicate topics ({original_topics} → {merged_topics})")
                return True
            else:
                print(f"  ⚠️  Cleanup failed")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            print(f"  Response: {response.text}")
            return False

    except Exception as e:
        print(f"  ❌ Error cleaning up topics: {e}")
        return False

def deduplicate_topics():
    """Use the server's deduplicate-topics endpoint for additional deduplication"""
    print("🔧 Running additional topic deduplication...")

    try:
        response = requests.post('http://localhost:3001/api/deduplicate-topics')

        if response.status_code == 200:
            result = response.json()
            print(f"  ✅ Deduplication completed: {result.get('success', False)}")

            if result.get('success'):
                dedup_result = result.get('result', {})
                print(f"  📊 Deduplication results: {dedup_result}")
                return True
            else:
                print(f"  ⚠️  Deduplication failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"  ❌ Error: {response.status_code}")
            print(f"  Response: {response.text}")
            return False

    except Exception as e:
        print(f"  ❌ Error deduplicating topics: {e}")
        return False

def check_final_results():
    """Check the final results after regeneration"""
    print("🔍 Checking final results...")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code != 200:
            print(f"  ❌ Error fetching final topics: {response.status_code}")
            return False
            
        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"  ❌ API error: {api_data.get('message', 'Unknown error')}")
            return False
            
        all_topics = api_data.get('topics', [])
        people = [t for t in all_topics if t.get('category') == 'people']
        topics = [t for t in all_topics if t.get('category') != 'people']
        
        print(f"  📊 Final count: {len(topics)} topics, {len(people)} people")
        
        # Show category breakdown
        categories = {}
        for topic in topics:
            category = topic.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
        
        print("  📋 Categories:")
        for category, count in sorted(categories.items()):
            print(f"    - {category}: {count}")
        
        # Check for noun-only compliance
        verb_topics = []
        for topic in topics:
            name = topic['name'].lower()
            # Simple check for verb-like patterns
            if any(pattern in name for pattern in ['开发', '设计', '搭建', '实现', '调研', '改进', '重构']):
                verb_topics.append(topic['name'])
        
        if verb_topics:
            print(f"  ⚠️  Found {len(verb_topics)} potentially verb-based topics:")
            for vt in verb_topics[:5]:
                print(f"    - {vt}")
            if len(verb_topics) > 5:
                print(f"    ... and {len(verb_topics) - 5} more")
        else:
            print("  ✅ All topics appear to be noun-based")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error checking results: {e}")
        return False

def main():
    """Main cleanup and regeneration process"""
    print("🚀 Starting comprehensive topic cleanup and regeneration...")
    print("="*70)

    # Step 1: Backup current topics
    backup_file = backup_current_topics()
    if not backup_file:
        print("❌ Failed to create backup - aborting")
        return False

    # Step 2: Rebuild topics from scratch (this clears and regenerates)
    if not rebuild_topics_from_scratch():
        print("❌ Failed to rebuild topics from scratch")
        return False

    # Step 3: Clean up any remaining duplicates
    if not cleanup_duplicate_topics():
        print("⚠️  Topic cleanup had issues, but continuing...")

    # Step 4: Run additional deduplication
    if not deduplicate_topics():
        print("⚠️  Additional deduplication had issues, but continuing...")

    # Step 5: Check final results
    if not check_final_results():
        print("❌ Failed to verify results")
        return False

    print("\n" + "="*70)
    print("✅ Topic cleanup and regeneration completed successfully!")
    print(f"\n📁 Backup saved: {backup_file}")
    print("\n💡 Next steps:")
    print("   1. Review topics in the web interface")
    print("   2. Check if any manual consolidation is still needed")
    print("   3. Adjust topic visibility settings")
    print("   4. Test the topic graph functionality")

    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Cleanup and regeneration failed!")
        exit(1)
