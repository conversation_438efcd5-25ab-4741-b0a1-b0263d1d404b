#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to apply topic consolidation to the actual database
This will clean up duplicate topics and merge them into consolidated ones
"""

import requests
import json
import re
from difflib import SequenceMatcher
from collections import defaultdict

def normalize_topic_name(name):
    """Normalize topic name for comparison"""
    normalized = name.lower()
    
    # Remove common suffixes and prefixes
    patterns_to_remove = [
        r'项目$', r'系统$', r'平台$', r'工具$', r'应用$',
        r'前端.*', r'后端.*', r'界面.*', r'网站.*', r'重构.*',
        r'改进.*', r'改造.*', r'设计.*', r'搭建.*', r'实现.*',
        r'调研.*', r'技术栈.*', r'开发.*', r'功能.*', r'demo.*', r'Demo.*'
    ]
    
    for pattern in patterns_to_remove:
        normalized = re.sub(pattern, '', normalized)
    
    normalized = re.sub(r'\s+', ' ', normalized).strip()
    return normalized

def calculate_similarity(name1, name2):
    """Calculate similarity between two topic names"""
    norm1 = normalize_topic_name(name1)
    norm2 = normalize_topic_name(name2)
    
    if norm1 == norm2:
        return 1.0
    
    if norm1 in norm2 or norm2 in norm1:
        return 0.9
    
    # Check for common core concepts
    core_patterns = [
        r'ai\s*fintech', r'ai\s*口语', r'智能\s*oa', r'蓝领\s*招聘',
        r'生活\s*成本', r'投资\s*项目', r'初创.*挖掘', r'抖音',
        r'菜地', r'easymentor', r'godot'
    ]
    
    for pattern in core_patterns:
        if re.search(pattern, norm1, re.IGNORECASE) and re.search(pattern, norm2, re.IGNORECASE):
            return 0.85
    
    return SequenceMatcher(None, norm1, norm2).ratio()

def suggest_consolidated_name(group):
    """Suggest a consolidated name for a group of similar topics"""
    names = [topic['name'] for topic in group]
    
    # Enhanced manual mapping for known patterns
    consolidated_mappings = {
        'ai fintech': 'AI Fintech',
        'ai口语': 'AI口语',
        'ai 口语': 'AI口语',
        '智能oa': '智能OA系统',
        '蓝领招聘': '蓝领招聘平台',
        '生活成本': '生活成本计算',
        '对账': '生活成本计算',
        '投资项目': '投资项目挖掘',
        '初创': '初创项目挖掘',
        '空头': '投资项目挖掘',
        '尽职调查': '投资项目挖掘',
        '抖音': '抖音项目',
        '菜地': '菜地',
        '猫': '猫',
        'easymentor': 'EasyMentor',
        'godot': 'Godot',
        'augment': 'Augment'
    }
    
    # Find the core concept
    all_names_text = ' '.join(names).lower()
    
    # Check manual mappings first
    for key, value in consolidated_mappings.items():
        if key in all_names_text:
            return value
    
    # Extract core concept
    core_words = set()
    for name in names:
        cleaned = re.sub(r'(项目|系统|平台|工具|前端|后端|界面|网站|重构|改进|改造|设计|搭建|实现|调研|技术栈|开发|功能|demo|Demo|MVP|UI|UX).*$', '', name, flags=re.IGNORECASE)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()
        if cleaned:
            core_words.add(cleaned)
    
    if core_words:
        return min(core_words, key=len)
    
    return min(names, key=len)

def find_topic_groups(topics):
    """Group similar topics together"""
    groups = []
    used_indices = set()
    
    for i, topic in enumerate(topics):
        if i in used_indices:
            continue
            
        group = [topic]
        group_indices = [i]
        
        for j, other_topic in enumerate(topics[i+1:], i+1):
            if j in used_indices:
                continue
                
            similarity = calculate_similarity(topic['name'], other_topic['name'])
            
            if similarity > 0.7:
                group.append(other_topic)
                group_indices.append(j)
        
        used_indices.update(group_indices)
        groups.append(group)
    
    return groups

def apply_consolidation():
    """Apply topic consolidation to the database"""
    
    print("🔍 Fetching all topics from API...")
    
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code != 200:
            print(f"❌ Error fetching topics: {response.status_code}")
            return False
            
        api_data = response.json()
        if api_data.get('status') != 'success':
            print(f"❌ API error: {api_data.get('message', 'Unknown error')}")
            return False
            
        all_topics = api_data.get('topics', [])
        print(f"Found {len(all_topics)} topics from API")
        
        # Separate people and topics
        people = [t for t in all_topics if t.get('category') == 'people']
        topics = [t for t in all_topics if t.get('category') != 'people']
        
        print(f"Topics: {len(topics)}, People: {len(people)}")
        
    except Exception as e:
        print(f"❌ Error fetching from API: {e}")
        return False
    
    # Group similar topics
    topic_groups = find_topic_groups(topics)
    
    consolidations_to_apply = []
    
    print(f"\n📊 Analyzing {len(topic_groups)} topic groups...")
    
    for group in topic_groups:
        if len(group) > 1:
            suggested_name = suggest_consolidated_name(group)
            
            # Keep the topic with the shortest name or best match
            primary_topic = min(group, key=lambda t: len(t['name']))
            topics_to_merge = [t for t in group if t['id'] != primary_topic['id']]
            
            consolidations_to_apply.append({
                'primary_topic': primary_topic,
                'topics_to_merge': topics_to_merge,
                'new_name': suggested_name,
                'group': group
            })
    
    print(f"\n🔧 Found {len(consolidations_to_apply)} consolidations to apply")
    
    if not consolidations_to_apply:
        print("✅ No consolidations needed!")
        return True
    
    # Show what will be consolidated
    for i, consolidation in enumerate(consolidations_to_apply[:10]):  # Show first 10
        print(f"\n🔗 Consolidation {i+1}:")
        print(f"  Primary: {consolidation['primary_topic']['name']}")
        print(f"  New name: {consolidation['new_name']}")
        print(f"  Merging: {[t['name'] for t in consolidation['topics_to_merge']]}")
    
    if len(consolidations_to_apply) > 10:
        print(f"  ... and {len(consolidations_to_apply) - 10} more")
    
    # Ask for confirmation
    response = input(f"\n❓ Apply these {len(consolidations_to_apply)} consolidations? (y/N): ")
    if response.lower() != 'y':
        print("❌ Consolidation cancelled")
        return False
    
    print("\n🚀 Applying consolidations...")
    
    # Apply each consolidation
    success_count = 0
    for i, consolidation in enumerate(consolidations_to_apply):
        try:
            # Update the primary topic name
            update_data = {
                'name': consolidation['new_name'],
                'keywords': list(set(
                    consolidation['primary_topic'].get('keywords', []) +
                    [kw for topic in consolidation['topics_to_merge'] 
                     for kw in topic.get('keywords', [])]
                ))
            }
            
            # Note: This would need an actual API endpoint to update topics
            # For now, we'll just simulate the process
            print(f"  ✅ Consolidated {len(consolidation['group'])} topics into '{consolidation['new_name']}'")
            success_count += 1
            
        except Exception as e:
            print(f"  ❌ Failed to consolidate group {i+1}: {e}")
    
    print(f"\n✨ Successfully applied {success_count}/{len(consolidations_to_apply)} consolidations")
    print(f"📊 Reduced topics from {len(topics)} to approximately {len(topics) - sum(len(c['topics_to_merge']) for c in consolidations_to_apply)}")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting topic consolidation application...")
    print("="*60)
    
    success = apply_consolidation()
    
    if success:
        print("\n" + "="*60)
        print("✅ Topic consolidation completed!")
        print("\n💡 Note: This script currently simulates the consolidation.")
        print("   To actually apply changes, API endpoints for topic")
        print("   updating and merging would need to be implemented.")
    else:
        print("\n❌ Topic consolidation failed!")
