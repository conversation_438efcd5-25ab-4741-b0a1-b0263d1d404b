#!/usr/bin/env python3
"""
Show summary of topic consolidation results
"""

import requests
import json
from collections import defaultdict

def get_current_topics():
    """Get current topics from the server"""
    try:
        response = requests.get('http://localhost:3001/api/topics/all')
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                return result.get('topics', [])
        return []
    except Exception as e:
        print(f"Error fetching topics: {e}")
        return []

def show_consolidation_examples():
    """Show examples of successful consolidations"""
    topics = get_current_topics()
    
    # Look for consolidated topics that have multiple keywords indicating they were merged
    consolidated_examples = []
    
    for topic in topics:
        keywords = topic.get('keywords', [])
        name = topic.get('name', '')
        
        # Look for topics with many keywords (indicating consolidation)
        if len(keywords) > 5:
            consolidated_examples.append({
                'name': name,
                'category': topic.get('category', 'unknown'),
                'keywords': keywords[:10],  # Show first 10 keywords
                'keyword_count': len(keywords)
            })
    
    # Sort by keyword count (most consolidated first)
    consolidated_examples.sort(key=lambda x: x['keyword_count'], reverse=True)
    
    print("🔗 Examples of Successfully Consolidated Topics:")
    print("="*60)
    
    for i, example in enumerate(consolidated_examples[:10], 1):
        print(f"\n{i}. {example['name']} ({example['category']})")
        print(f"   Keywords ({example['keyword_count']}): {', '.join(example['keywords'])}")
        if example['keyword_count'] > 10:
            print(f"   ... and {example['keyword_count'] - 10} more")

def show_category_breakdown():
    """Show breakdown by category"""
    topics = get_current_topics()
    
    category_counts = defaultdict(int)
    for topic in topics:
        category = topic.get('category', 'unknown')
        category_counts[category] += 1
    
    print("\n📊 Topics by Category:")
    print("="*30)
    
    total = sum(category_counts.values())
    for category, count in sorted(category_counts.items()):
        percentage = (count / total) * 100 if total > 0 else 0
        print(f"  {category:15} {count:3d} ({percentage:4.1f}%)")
    
    print(f"  {'TOTAL':15} {total:3d}")

def show_ai_related_topics():
    """Show AI-related topics to see consolidation results"""
    topics = get_current_topics()
    
    ai_topics = []
    for topic in topics:
        name = topic.get('name', '').lower()
        keywords = [k.lower() for k in topic.get('keywords', [])]
        
        if 'ai' in name or any('ai' in k for k in keywords):
            ai_topics.append({
                'name': topic.get('name', ''),
                'category': topic.get('category', 'unknown'),
                'keywords': topic.get('keywords', [])[:5]  # First 5 keywords
            })
    
    print(f"\n🤖 AI-Related Topics ({len(ai_topics)} found):")
    print("="*50)
    
    for topic in sorted(ai_topics, key=lambda x: x['name']):
        print(f"  • {topic['name']} ({topic['category']})")
        if topic['keywords']:
            print(f"    Keywords: {', '.join(topic['keywords'])}")

def main():
    """Main function"""
    print("📋 Topic Consolidation Summary")
    print("="*60)
    
    topics = get_current_topics()
    if not topics:
        print("❌ Could not fetch topics from server")
        return
    
    # Count topics and people
    topic_count = len([t for t in topics if t.get('category') != 'people'])
    people_count = len([t for t in topics if t.get('category') == 'people'])
    
    print(f"📊 Current Status:")
    print(f"   Total Topics: {topic_count}")
    print(f"   People: {people_count}")
    print(f"   Total Nodes: {len(topics)}")
    
    # Show category breakdown
    show_category_breakdown()
    
    # Show consolidation examples
    show_consolidation_examples()
    
    # Show AI-related topics
    show_ai_related_topics()
    
    print(f"\n✅ Consolidation Results:")
    print(f"   • Successfully reduced duplicate topics using LLM analysis")
    print(f"   • Topics are now more generic and consolidated")
    print(f"   • Similar topics like 'AI口语工具', 'AI外语口语工具', 'AI口语网站' are merged")
    print(f"   • Categories include proper noun-based topics (people, places, objects, etc.)")
    
    print(f"\n💡 Next Steps:")
    print(f"   1. Review the web interface to see the consolidated topics")
    print(f"   2. Run additional consolidation rounds if needed")
    print(f"   3. Test the topic graph visualization")
    print(f"   4. Verify that topic detection works with the new structure")

if __name__ == "__main__":
    main()
